# Putt-Putt Score Capture App

## Essential Features for a Putt-Putt Score Capture App

.NET MAUI Putt-Putt app core features:

### 1. Game Setup & Player Management

* **Player Profiles:**
  * Ability to add, edit, and delete players.
  * Store player names (and optionally, unique colors or avatars for easy identification).
  * Option to mark favorite or frequent players for quick selection.
* **New Game Creation:**
  * Intuitive flow to start a new game.
  * Select multiple players from existing profiles for the current game.
  * Option to add "guest" players on the fly for a single game without saving a full profile.
  * Choose the number of holes (e.g., 9-hole, 18-hole, or custom).
* **Course Management (Optional but Recommended):**
  * Ability to define and save different Putt-Putt courses (e.g., "Main Course," "Adventure Course").
  * For each course, allow setting the par for each hole.
  * Allow for QR Code scanning that will autoconfigure the course details.
  * Optionally, course details downloadable based on location proximity to the course.

### 2. Live Scoring & Display

* **Intuitive Score Entry:**
  * Clear interface to input strokes for each player on the current hole.
  * Easy navigation between players and holes (e.g., swipe gestures or clear buttons).
  * Visual indication of the current player and hole.
  * Ability to quickly correct an entered score.
  * Support for a maximum stroke limit per hole (e.g., if you hit 6 or 7, it caps at that and you move on).
* **Real-time Leaderboard:**
  * Continuously updated display of each player's total score.
  * Show current standings (e.g., "Player A: -2", "Player B: +3").
  * Option to view scores per hole for all players.

### 3. Game History & Statistics

* **Save & Load Games:**
  * Automatic or manual saving of game progress.
  * Ability to resume an unfinished game.
  * Save completed games with date, course, and player details.
* **Game History:**
  * List of all past games.
  * Ability to view the full scorecard of any completed game.
* **Basic Player Statistics:**
  * Track individual player averages.
  * Record best scores for a game or course.
  * Count of "hole-in-ones" or "aces."

### 4. User Experience & Usability

* **Offline Functionality:** The app should work seamlessly without an internet connection, as Putt-Putt courses might have spotty reception.
* **Responsive UI:** Designed to look good and be easy to use on both phone and tablet screens, across Android and iOS, leveraging .NET MAUI's capabilities.
* **Data Persistence:** Scores and player data should be saved locally on the device (consider SQLite or file storage, or even cloud sync if you want to get fancy later).
* **"Undo" Functionality:** A simple way to revert the last score entry.
* **Share Results:** Option to share the final scorecard or summary (e.g., as text, image, or PDF) via messaging apps or social media.
* **Clear Visual Feedback:** Animations or sounds for successful score entry or a hole-in-one.

### 5. Mini-wiki rule explainer

* **Game rules:** Players should be able to view general rules (not course specific, e.g. `general-course-rules.md`)
